# Manual Migration Application

Since the CLI is requesting a password, you can apply the migration manually through the Supabase Dashboard:

## Steps:

1. Go to the Supabase Dashboard: https://supabase.com/dashboard/project/jihaizhvpddinhdysscd/sql
2. Copy and paste the contents of `supabase/migrations/20250907120000_fix_whatsapp_rls_and_deletion.sql`
3. Run the SQL to apply the migration

## Migration File Location:
`supabase/migrations/20250907120000_fix_whatsapp_rls_and_deletion.sql`

## What this migration does:
- Fixes RLS policies for WhatsApp conversations and messages
- Adds proper DELETE permissions
- Creates a safe deletion function with permission checks
- Adds performance indexes
- Ensures CASCADE deletion for messages when conversations are deleted

## After applying the migration:
The conversation deletion issue should be resolved, and conversations will be properly deleted from the database when deleted from the UI.
